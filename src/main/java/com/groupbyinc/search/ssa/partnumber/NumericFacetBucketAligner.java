package com.groupbyinc.search.ssa.partnumber;

import com.groupbyinc.proto.commandcenter.config.AttributeMessage;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.Range;
import com.groupbyinc.search.ssa.core.rule.PinnedRefinement;
import com.groupbyinc.search.ssa.util.AttributeUtils;

import io.micronaut.core.annotation.NonNull;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

import static com.groupbyinc.search.ssa.core.navigation.NavigationRefinement.applyPinnedRefinements;
import static com.groupbyinc.search.ssa.core.navigation.NavigationType.RANGE;
import static com.groupbyinc.search.ssa.mongo.MongoConstants.INDEXABLES_PREFIX;

/**
 * Service responsible for aligning numeric facet buckets between main (Google) and secondary (Mongo)
 * search results during PartNumber Expanded Search.
 *
 * <p>This service preserves Google's bucket structure and distributes Mongo products into appropriate
 * buckets by iterating through actual Mongo records, handling edge cases for values outside Google
 * bucket ranges.</p>
 */
@Slf4j
@Singleton
public class NumericFacetBucketAligner {

    /**
     * Aligns numeric facet buckets between main (Google) and secondary (Mongo) navigations.
     * This method preserves Google's bucket structure and distributes Mongo products into
     * appropriate buckets by iterating through actual Mongo records.
     *
     * @param mainNav           The main navigation (Google) with bucket structure to preserve
     * @param secondaryNav      The secondary navigation (Mongo) to be aligned
     * @param secondaryRecords  The actual Mongo records to distribute into buckets
     * @param pinnedRefinements Pinned refinements to apply after alignment
     * @param searchParameters  Search parameters containing attribute configurations for field transformation
     * @return A new Navigation with aligned buckets containing correct product counts
     */
    public Navigation alignBuckets(Navigation mainNav,
                                   Navigation secondaryNav,
                                   List<Record> secondaryRecords,
                                   List<PinnedRefinement> pinnedRefinements,
                                   @NonNull SearchParameters searchParameters) {

        if (!shouldAlignBuckets(mainNav, secondaryNav)) {
            log.debug("Bucket alignment not applicable for navigations with different types or fields");
            return mainNav;
        }

        if (secondaryRecords == null || secondaryRecords.isEmpty()) {
            // No secondary records to align, return the main navigation as is
            return mainNav;
        }

        var mainRanges = mainNav.getRefinements();
        var fieldName = transformNavigationFieldForMongo(secondaryNav, searchParameters);

        log.debug("Aligning buckets for field '{}' with {} main ranges and {} secondary records",
                  fieldName, mainRanges.size(), secondaryRecords.size());

        var alignedRefinements = new ArrayList<NavigationRefinement>();

        // Add main navigation buckets with their existing counts
        for (var mainRef : mainRanges) {
            long mainCount = mainRef.getCount() != null ? mainRef.getCount() : 0;
            alignedRefinements.add(NavigationRefinement.rangeRefinement(mainRef.getRange(), mainCount));
        }

        // Add edge case buckets (if any) with zero counts
        alignedRefinements.addAll(createEdgeCaseBuckets(mainNav, secondaryRecords, fieldName, searchParameters));

        // Single iteration through records to update counts for all buckets
        updateBucketCounts(secondaryRecords, fieldName, alignedRefinements, searchParameters);

        alignedRefinements.sort(defineSortOrder());
        applyPinnedRefinements(mainNav.getField(), alignedRefinements, pinnedRefinements);

        return mainNav.toBuilder()
            .refinements(alignedRefinements)
            .build();
    }

    /**
     * Determines if bucket alignment should be applied to the given navigations.
     *
     * @param mainNav      The main navigation (Google results)
     * @param secondaryNav The secondary navigation (Mongo results)
     * @return true if both navigations are RANGE type with the same field
     */
    private boolean shouldAlignBuckets(Navigation mainNav, Navigation secondaryNav) {
        return mainNav.getType() == RANGE &&
               secondaryNav.getType() == RANGE &&
               mainNav.getField().equals(secondaryNav.getField());
    }

    /**
     * Counts how many secondary records fall within the specified range for the given field.
     *
     * @param records          The records to examine
     * @param fieldName        The field name to extract values from
     * @param refinements      All bucket refinements to update (modified in place)
     * @param searchParameters The search parameters containing attribute configurations
     */
    private void updateBucketCounts(List<Record> records,
                                   String fieldName,
                                   List<NavigationRefinement> refinements,
                                   SearchParameters searchParameters) {
        // Initialize the count array for secondary records
        var secondaryCounts = new long[refinements.size()];

        // Single iteration through all records
        for (var record : records) {
            var numericValue = extractNumericValue(record, fieldName, searchParameters);
            if (numericValue != null) {
                // Check against all buckets for this record
                for (int i = 0; i < refinements.size(); i++) {
                    if (isValueInRange(numericValue, refinements.get(i).getRange())) {
                        secondaryCounts[i]++;
                    }
                }
            }
        }

        // Update refinements in place with combined counts
        for (int i = 0; i < refinements.size(); i++) {
            var refinement = refinements.get(i);
            long existingCount = refinement.getCount() != null ? refinement.getCount() : 0;
            long totalCount = existingCount + secondaryCounts[i];
            // Use toBuilder to preserve all properties and update the count
            refinements.set(i, refinement.toBuilder().count(totalCount).build());
        }
    }

    /**
     * Extracts a numeric value from a record's metadata for the specified field.
     *
     * @param record           The record to extract from
     * @param fieldName        The field name/path to look for (e.g., "priceInfo.price")
     * @param searchParameters The search parameters containing attribute configurations
     * @return The numeric value as Double, or null if not found or not numeric
     */
    private Double extractNumericValue(Record record, String fieldName, SearchParameters searchParameters) {
        if (record == null || record.getMetadata() == null) {
            return null;
        }

        // Get the attribute configuration from search parameters
        var attributeConfigurations = searchParameters.getMerchandisingConfiguration().attributeConfigurations();
        var attributeConfig = attributeConfigurations.get(fieldName);

        if (attributeConfig == null) {
            log.debug("No attribute configuration found for field '{}'", fieldName);
            // Fallback: create a temporary configuration with the field path
            attributeConfig = AttributeConfiguration.builder()
                .key(fieldName)
                .path(fieldName)
                .type(AttributeMessage.AttributeType.NUMERICAL)
                .build();
        }

        var values = AttributeUtils.getAttributeValueFromRecord(record, attributeConfig);

        if (values.isEmpty()) {
            return null;
        }

        // Get the first numeric value
        var firstValue = values.getFirst();
        if (firstValue instanceof Number) {
            return ((Number) firstValue).doubleValue();
        }

        // Fallback: try to parse as a string
        try {
            if (firstValue instanceof String) {
                return Double.parseDouble((String) firstValue);
            }
        } catch (NumberFormatException e) {
            log.debug("Could not parse numeric value '{}' for field '{}'", firstValue, fieldName);
        }

        return null;
    }

    /**
     * Checks if a numeric value falls within the specified range.
     * Handles {@code -Infinity} and {@code +Infinity} boundaries properly.
     *
     * @param value The numeric value to check
     * @param range The range to check against
     * @return true if the value falls within the range
     */
    private boolean isValueInRange(Double value, Range range) {
        if (value == null || range == null) {
            return false;
        }

        var low = range.low() != null ? range.low() : Double.NEGATIVE_INFINITY;
        var high = range.high() != null ? range.high() : Double.POSITIVE_INFINITY;

        // Range is typically [low, high) - inclusive on low, exclusive on high
        // But handle the edge case where high is infinity
        if (Double.isInfinite(high) && high > 0) {
            return value >= low; // [low, +∞)
        } else if (Double.isInfinite(low) && low < 0) {
           return value < high; // (-∞, high)
        } else {
            return value >= low && value < high; // [low, high)
        }
    }

    /**
     * Creates additional buckets for secondary records that fall outside the main navigation ranges.
     *
     * @param mainNav          The main navigation with the existing bucket structure
     * @param records          The secondary records to check for edge cases
     * @param fieldName        The field name to use for extracting values from records
     * @param searchParameters The search parameters containing attribute configurations
     * @return List of additional refinements for edge cases
     */
    private List<NavigationRefinement> createEdgeCaseBuckets(Navigation mainNav,
                                                             List<Record> records,
                                                             String fieldName,
                                                             SearchParameters searchParameters) {
        var edgeCaseBuckets = new ArrayList<NavigationRefinement>();

        // Find main range boundaries
        var mainRanges = mainNav.getRefinements().stream()
            .map(NavigationRefinement::getRange)
            .toList();

        var mainMin = mainRanges.stream()
            .mapToDouble(r -> r.low() != null ? r.low() : Double.NEGATIVE_INFINITY)
            .filter(v -> !Double.isInfinite(v))
            .min().orElse(Double.NEGATIVE_INFINITY);

        var mainMax = mainRanges.stream()
            .mapToDouble(r -> r.high() != null ? r.high() : Double.POSITIVE_INFINITY)
            .filter(v -> !Double.isInfinite(v))
            .max().orElse(Double.POSITIVE_INFINITY);

        // Find actual min/max values from secondary records using the provided field name
        var recordValues = records.stream()
            .map(record -> extractNumericValue(record, fieldName, searchParameters))
            .filter(Objects::nonNull)
            .toList();

        if (recordValues.isEmpty()) {
            return edgeCaseBuckets;
        }

        var recordMin = recordValues.stream().mapToDouble(Double::doubleValue).min().orElse(Double.NaN);
        var recordMax = recordValues.stream().mapToDouble(Double::doubleValue).max().orElse(Double.NaN);

        // Create a bucket for values below the main minimum
        if (Double.isFinite(mainMin) && recordMin < mainMin) {
            var roundedMin = roundDownToReasonableValue(recordMin);
            var belowRange = new Range(roundedMin, mainMin);
            edgeCaseBuckets.add(NavigationRefinement.rangeRefinement(belowRange, 0L));
        }

        // Create a bucket for values above the main maximum
        if (Double.isFinite(mainMax) && recordMax >= mainMax) {
            var roundedMax = roundUpToReasonableValue(recordMax);
            var aboveRange = new Range(mainMax, roundedMax);
            edgeCaseBuckets.add(NavigationRefinement.rangeRefinement(aboveRange, 0L));
        }

        return edgeCaseBuckets;
    }

    /**
     * Defines the sort order for refinements based on the navigation's sort configuration.
     * For RANGE navigations, sorts descending by range low value (numerical order).
     *
     * @return Comparator for sorting NavigationRefinements
     */
    private Comparator<NavigationRefinement> defineSortOrder() {
        // For RANGE navigations: descending by range low value (numerical order)
        return Comparator.comparing((NavigationRefinement r) -> r.getRange().low(),
            Comparator.nullsLast(Comparator.naturalOrder())).reversed();
    }

    /**
     * Rounds a value down to a reasonable boundary based on the magnitude of the value.
     * This creates more user-friendly bucket boundaries (e.g., 0, 10, 100, 1000).
     *
     * @param value The value to round down
     * @return A reasonable lower boundary
     */
    private double roundDownToReasonableValue(double value) {
        if (value == 0.0) {
            return 0.0;
        }

        // Handle negative values
        if (value < 0) {
            return -roundUpToReasonableValue(-value);
        }

        // Find the order of magnitude
        var magnitude = Math.pow(10, Math.floor(Math.log10(value)));

        // Round down to nearest multiple of the appropriate step size
        if (value < magnitude * 2) {
            return Math.floor(value / magnitude) * magnitude;
        } else if (value < magnitude * 5) {
            return Math.floor(value / (magnitude * 2)) * (magnitude * 2);
        } else {
            return Math.floor(value / (magnitude * 5)) * (magnitude * 5);
        }
    }

    /**
     * Rounds a value up to a reasonable boundary based on the magnitude of the value.
     * The algorithm uses three-step sizes based on the value's position within its magnitude,
     * where magnitude is the largest power of 10 that's ≤ the value.
     *
     * <p>This method always advances to the NEXT boundary using floor + stepSize:</p>
     * <ul>
     *   <li>For values ≤ magnitude × 2: Step size = magnitude</li>
     *   <li>For values ≤ magnitude × 5: Step size = magnitude × 2</li>
     *   <li>For values > magnitude × 5: Step size = magnitude × 5</li>
     * </ul>
     *
     * <p>Examples:</p>
     * <ul>
     *   <li>15.0 → 20.0 (magnitude=10, step=10, floor(15/10)*10+10 = 20)</li>
     *   <li>35.0 → 40.0 (magnitude=10, step=20, floor(35/20)*20+20 = 40)</li>
     *   <li>75.0 → 100.0 (magnitude=10, step=50, floor(75/50)*50+50 = 100)</li>
     *   <li>400.0 → 600.0 (magnitude=100, step=200, floor(400/200)*200+200 = 600)</li>
     * </ul>
     *
     * <p>Magnitude examples:</p>
     * <ul>
     *   <li>For 150: magnitude = 100 (10²)</li>
     *   <li>For 25: magnitude = 10 (10¹)</li>
     *   <li>For 3.7: magnitude = 1 (10⁰)</li>
     *   <li>For 0.15: magnitude = 0.1 (10⁻¹)</li>
     * </ul>
     *
     * @param value The value to round up
     * @return A reasonable upper boundary that's always greater than the input value
     */
    private double roundUpToReasonableValue(double value) {
        if (value == 0.0) {
            return 1.0;
        }

        // Handle negative values
        if (value < 0) {
            return -roundDownToReasonableValue(-value);
        }

        // Find the order of magnitude
        var magnitude = Math.pow(10, Math.floor(Math.log10(value)));

        // Round up to the next multiple of the appropriate step size
        if (value <= magnitude * 2) {
            return Math.floor(value / magnitude) * magnitude + magnitude;
        } else if (value <= magnitude * 5) {
            var stepSize = magnitude * 2;
            return Math.floor(value / stepSize) * stepSize + stepSize;
        } else {
            var stepSize = magnitude * 5;
            return Math.floor(value / stepSize) * stepSize + stepSize;
        }
    }

    private String transformNavigationFieldForMongo(Navigation navigation, @NonNull SearchParameters searchParameters) {
        var originalField = navigation.getField();
        var attributeConfigurations = searchParameters.getMerchandisingConfiguration().attributeConfigurations();

        return INDEXABLES_PREFIX + AttributeUtils.transformFieldForMongo(originalField, attributeConfigurations);
    }
}
