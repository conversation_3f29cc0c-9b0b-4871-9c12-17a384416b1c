package com.groupbyinc.search.ssa.partnumber;

import com.groupbyinc.search.ssa.core.MerchandisingConfiguration;
import com.groupbyinc.search.ssa.core.Pagination;
import com.groupbyinc.search.ssa.core.Record;
import com.groupbyinc.search.ssa.core.SearchParameters;
import com.groupbyinc.search.ssa.core.attribute.AttributeConfiguration;
import com.groupbyinc.search.ssa.core.navigation.Navigation;
import com.groupbyinc.search.ssa.core.navigation.NavigationRefinement;
import com.groupbyinc.search.ssa.core.navigation.NavigationType;
import com.groupbyinc.search.ssa.core.navigation.Range;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.groupbyinc.search.ssa.stub.TestData.APPAREL_MERCHANDISER;
import static org.assertj.core.api.Assertions.assertThat;

class NumericFacetBucketAlignerTest {

    // Test constants
    private static final String PRICE = "price";
    private static final String TEST_PRODUCT_TITLE = "Test Product";
    private static final String COLLECTION = "collection";
    private static final String PRODUCT_PREFIX = "product-";
    private static final String STRING_PRODUCT_ID = "product-string";
    private static final String NULL_PRODUCT_ID = "product-null";
    private static final String INVALID_PRICE_VALUE = "not_a_number";
    private static final String DUMMY_VALUE = "dummy";

    private NumericFacetBucketAligner bucketAligner;
    private SearchParameters searchParameters;

    @BeforeEach
    void setUp() {
        bucketAligner = new NumericFacetBucketAligner();

        // Create attribute configuration for the price field
        var priceAttributeConfig = AttributeConfiguration.builder()
            .key(PRICE)
            .path("priceInfo.price")
            .build();

        // Create a merchandising configuration with attribute configurations
        var merchandisingConfig = MerchandisingConfiguration.builder()
            .attributeConfigurations(Map.of(PRICE, priceAttributeConfig))
            .build();

        // Create search parameters with the merchandising configuration
        searchParameters = SearchParameters.builder()
            .merchandisingConfiguration(merchandisingConfig)
            .partNumberSearchEnabled(true)
            .pagination(new Pagination(30, 0L))
            .build();
    }

    @Test
    @DisplayName("Should align buckets for basic scenario with records distributed across Google ranges")
    void testAlignBuckets_basicAlignment() {
        // given - Google navigation with the specific bucket structure
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(0.0, 10.0), 5),
            NavigationRefinement.rangeRefinement(new Range(10.0, 20.0), 3),
            NavigationRefinement.rangeRefinement(new Range(20.0, 30.0), 2)
        );
        var googleNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        // Mongo navigation
        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 100.0), 0))) // Dummy refinement
            .build();

        // Mongo records with specific price values
        var mongoRecords = List.of(
            createRecordWithPrice(5.0),   // Falls in [0-10)
            createRecordWithPrice(15.0),  // Falls in [10-20)
            createRecordWithPrice(15.5),  // Falls in [10-20)
            createRecordWithPrice(25.0)   // Falls in [20-30)
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(3);

        // Verify counts: `Google + Mongo` records in each bucket
        var firstBucket = findRefinementByRange(alignedRefinements, 0.0, 10.0);
        assertThat(firstBucket.getCount()).isEqualTo(6); // Google: 5 + Mongo: 1 record

        var secondBucket = findRefinementByRange(alignedRefinements, 10.0, 20.0);
        assertThat(secondBucket.getCount()).isEqualTo(5); // Google: 3 + Mongo: 2 records

        var thirdBucket = findRefinementByRange(alignedRefinements, 20.0, 30.0);
        assertThat(thirdBucket.getCount()).isEqualTo(3); // Google: 2 + Mongo: 1 record
    }

    @Test
    @DisplayName("Should handle edge cases with values outside Google bucket ranges")
    void testAlignBuckets_edgeCases() {
        // given - Google navigation with limited range
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(100.0, 200.0), 10),
            NavigationRefinement.rangeRefinement(new Range(200.0, 300.0), 15)
        );
        var googleNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 1000.0), 0))) // Dummy refinement
            .build();

        // Mongo records with values outside Google ranges
        var mongoRecords = List.of(
            createRecordWithPrice(50.0),   // Below Google min
            createRecordWithPrice(75.0),   // Below Google min
            createRecordWithPrice(150.0),  // Falls in [100-200)
            createRecordWithPrice(250.0),  // Falls in [200-300)
            createRecordWithPrice(350.0)   // Above the Google max
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(4); // 2 Google + 2 edge case buckets

        // Verify Google bucket counts
        var firstGoogleBucket = findRefinementByRange(alignedRefinements, 100.0, 200.0);
        assertThat(firstGoogleBucket.getCount()).isEqualTo(11); // Google: 10 + Mongo: 1

        var secondGoogleBucket = findRefinementByRange(alignedRefinements, 200.0, 300.0);
        assertThat(secondGoogleBucket.getCount()).isEqualTo(16); // Google: 15 + Mongo: 1

        // Verify edge case bucket for values below Google range (50.0, 75.0)
        // roundDownToReasonableValue(50.0): magnitude=10, value=50.0 is not < magnitude*5 (50.0)
        // So uses else branch: Math.floor(50.0/50.0)*50.0 = 50.0
        // Expected range: [50.0, 100.0)
        var belowMinBucket = findRefinementByRange(alignedRefinements, 50.0, 100.0);
        assertThat(belowMinBucket.getCount()).isEqualTo(2); // 2 records below Google min

        // Verify edge case bucket for values above Google range (350.0)
        // roundUpToReasonableValue(350.0): magnitude=100, step=200, result=400.0
        // Expected range: [300.0, 400.0)
        var aboveMaxBucket = findRefinementByRange(alignedRefinements, 300.0, 400.0);
        assertThat(aboveMaxBucket.getCount()).isEqualTo(1); // 1 record above Google max
    }

    @Test
    @DisplayName("Should handle boundary values correctly")
    void testAlignBuckets_boundaryValues() {
        // given
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(0.0, 50.0), 5),
            NavigationRefinement.rangeRefinement(new Range(50.0, 100.0), 8)
        );
        var googleNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 1000.0), 0))) // Dummy refinement
            .build();

        // Records at exact boundaries
        var mongoRecords = List.of(
            createRecordWithPrice(0.0),   // Exact lower bound of the first bucket
            createRecordWithPrice(50.0),  // Exact boundary between buckets
            createRecordWithPrice(100.0)  // Exact upper bound of the second bucket
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(3); // 2 Google buckets + 1 edge case bucket for 100.0

        var firstBucket = findRefinementByRange(alignedRefinements, 0.0, 50.0);
        assertThat(firstBucket.getCount()).isEqualTo(6); // Google: 5 + Mongo: 1 (0.0)

        var secondBucket = findRefinementByRange(alignedRefinements, 50.0, 100.0);
        assertThat(secondBucket.getCount()).isEqualTo(9); // Google: 8 + Mongo: 1 (50.0)

        // Edge case bucket for 100.0 record (above Google max of 100.0)
        var edgeCaseBucket = findRefinementByRange(alignedRefinements, 100.0, 200.0);
        assertThat(edgeCaseBucket.getCount()).isEqualTo(1); // Record: 100.0
    }

    @Test
    @DisplayName("Should handle Mongo infinity boundaries with finite Google buckets")
    void testAlignBuckets_mongoInfinityBoundaries() {
        // given - Google navigation with finite boundaries (realistic Google behavior)
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(50.0, 100.0), 5),
            NavigationRefinement.rangeRefinement(new Range(100.0, 150.0), 8)
        );
        var googleNav = Navigation.builder()
            .name("price")
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        // Mongo navigation with infinity boundaries (realistic Mongo behavior)
        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(
                NavigationRefinement.rangeRefinement(new Range(null, 75.0), 3),      // [-∞, 75)
                NavigationRefinement.rangeRefinement(new Range(75.0, 125.0), 6),     // [75, 125)
                NavigationRefinement.rangeRefinement(new Range(125.0, null), 4)      // [125, +∞)
            ))
            .build();

        // Records spread across infinity boundaries with finite Google buckets
        var mongoRecords = List.of(
            createRecordWithPrice(-100.0),  // Before Google range - will create edge bucket
            createRecordWithPrice(75.0),    // In Google [50-100) bucket
            createRecordWithPrice(125.0),   // In Google [100-150) bucket
            createRecordWithPrice(200.0),   // After Google range - will create edge bucket
            createRecordWithPrice(300.0)    // After Google range - same edge bucket
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(4); // 2 Google + 2 edge case buckets

        // Verify Google bucket [50-100) gets record: 75.0
        var firstGoogleBucket = findRefinementByRange(alignedRefinements, 50.0, 100.0);
        assertThat(firstGoogleBucket.getCount()).isEqualTo(6); // Google: 5 + Mongo: 1

        // Verify Google bucket [100-150) gets record: 125.0
        var secondGoogleBucket = findRefinementByRange(alignedRefinements, 100.0, 150.0);
        assertThat(secondGoogleBucket.getCount()).isEqualTo(9); // Google: 8 + Mongo: 1

        // Verify edge case bucket for values below Google range (-100.0)
        // roundDownToReasonableValue(-100.0): value < 0, so -roundUpToReasonableValue(100.0)
        // roundUpToReasonableValue(100.0): magnitude=100, step=100, result=200.0
        // roundDownToReasonableValue(-100.0) = -200.0
        // Expected range: [-200.0, 50.0)
        var belowMinBucket = findRefinementByRange(alignedRefinements, -200.0, 50.0);
        assertThat(belowMinBucket.getCount()).isEqualTo(1); // -100.0

        // Verify edge case bucket for values above Google range (200.0, 300.0)
        // roundUpToReasonableValue(300.0): magnitude=100, step=200, result=400.0
        // Expected range: [150.0, 400.0)
        var aboveMaxBucket = findRefinementByRange(alignedRefinements, 150.0, 400.0);
        assertThat(aboveMaxBucket.getCount()).isEqualTo(2); // 200.0, 300.0
    }

    @Test
    @DisplayName("Should handle single-sided infinity Mongo buckets with finite Google buckets")
    void testAlignBuckets_singleSidedInfinityBoundaries() {
        // given - Google navigation with finite boundaries
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(100.0, 200.0), 5),
            NavigationRefinement.rangeRefinement(new Range(200.0, 300.0), 8)
        );
        var googleNav = Navigation.builder()
            .name("price")
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        // Mongo navigation with single-sided infinity boundaries (realistic scenario)
        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(
                NavigationRefinement.rangeRefinement(new Range(null, 150.0), 7),     // [-∞, 150)
                NavigationRefinement.rangeRefinement(new Range(150.0, null), 12)     // [150, +∞)
            ))
            .build();

        // Records that span across infinity boundaries
        var mongoRecords = List.of(
            createRecordWithPrice(50.0),   // In [-∞, 150) - before Google range
            createRecordWithPrice(125.0),  // In [-∞, 150) and Google [100-200)
            createRecordWithPrice(175.0),  // In [150, +∞) and Google [100-200)
            createRecordWithPrice(250.0),  // In [150, +∞) and Google [200-300)
            createRecordWithPrice(400.0)   // In [150, +∞) - after Google range
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(4); // 2 Google + 2 edge case buckets (below-min and above-max)

        // Verify Google bucket [100-200) gets records: 125.0, 175.0
        var firstGoogleBucket = findRefinementByRange(alignedRefinements, 100.0, 200.0);
        assertThat(firstGoogleBucket.getCount()).isEqualTo(7); // Google: 5 + Mongo: 2

        // Verify Google bucket [200-300) gets record: 250.0
        var secondGoogleBucket = findRefinementByRange(alignedRefinements, 200.0, 300.0);
        assertThat(secondGoogleBucket.getCount()).isEqualTo(9); // Google: 8 + Mongo: 1

        // Verify edge case bucket for record 50.0 (below Google min of 100.0)
        var belowMinBucket = findRefinementByRange(alignedRefinements, 50.0, 100.0);
        assertThat(belowMinBucket.getCount()).isEqualTo(1); // Record: 50.0

        // Verify edge case bucket for record 400.0 (above Google max of 300.0)
        var aboveMaxBucket = findRefinementByRange(alignedRefinements, 300.0, 600.0);
        assertThat(aboveMaxBucket.getCount()).isEqualTo(1); // Record: 400.0
    }

    @Test
    @DisplayName("Should handle invalid non-numeric attribute values")
    void testAlignBuckets_mixedAttributeValues() {
        // given
        var googleRefinements = List.of(
            NavigationRefinement.rangeRefinement(new Range(0.0, 100.0), 5)
        );
        var googleNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(googleRefinements)
            .build();

        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 1000.0), 0))) // Dummy refinement
            .build();

        var mongoRecords = List.of(
            createRecordWithPrice(50.0),    // Valid numeric
            createRecordWithStringPrice(),  // Invalid stringT
            createRecordWithPrice(75.0),    // Valid numeric
            createRecordWithNullPrice()     // Null value
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(1);

        var bucket = alignedRefinements.getFirst();
        assertThat(bucket.getCount()).isEqualTo(7); // Google: 5 + Valid Mongo: 2 (ignores invalid values)
    }

    @Test
    @DisplayName("Should return main navigation when types don't match")
    void testAlignBuckets_nonMatchingTypes() {
        // given
        var googleNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 100.0), 5)))
            .build();

        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.VALUE) // Different type
            .refinements(List.of(NavigationRefinement.valueRefinement(DUMMY_VALUE, 0, false))) // Dummy refinement
            .build();

        var mongoRecords = List.of(createRecordWithPrice(50.0));

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then - Should return the main navigation unchanged
        assertThat(alignedNav).isEqualTo(googleNav);
    }

    @Test
    @DisplayName("Should return main navigation when fields don't match")
    void testAlignBuckets_nonMatchingFields() {
        // given
        var googleNav = Navigation.builder()
            .name("price")
            .field("field_price")
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 100.0), 5)))
            .build();

        var mongoNav = Navigation.builder()
            .name("weight")
            .field("field_weight") // Different field
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 1000.0), 0))) // Dummy refinement
            .build();

        var mongoRecords = List.of(createRecordWithPrice(50.0));

        // when
        var alignedNav = bucketAligner.alignBuckets(googleNav, mongoNav, mongoRecords, List.of(), searchParameters);

        // then - Should return the main navigation unchanged
        assertThat(alignedNav).isEqualTo(googleNav);
    }

    @Test
    @DisplayName("Should handle missing attribute configuration")
    void testAlignBuckets_withMissingAttributeConfiguration() {
        // given - SearchParameters with empty attribute configurations
        var emptyMerchandisingConfig = MerchandisingConfiguration.builder()
            .attributeConfigurations(Map.of()) // No attribute configurations
            .build();

        var emptySearchParameters = SearchParameters.builder()
            .merchandisingConfiguration(emptyMerchandisingConfig)
            .partNumberSearchEnabled(true)
            .pagination(new Pagination(30, 0L))
            .build();

        var googleNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 10.0), 5)))
            .build();

        var mongoNav = Navigation.builder()
            .name(PRICE)
            .field(PRICE)
            .type(NavigationType.RANGE)
            .refinements(List.of(NavigationRefinement.rangeRefinement(new Range(0.0, 20.0), 2)))
            .build();

        var metadata = createMongoMetadataWithPrice(5.0);

        var mongoRecord = Record.of(
            APPAREL_MERCHANDISER,
            COLLECTION,
            "test-id",
            "test-id",
            TEST_PRODUCT_TITLE,
            metadata
        );

        // when
        var alignedNav = bucketAligner.alignBuckets(
            googleNav, mongoNav, List.of(mongoRecord), List.of(), emptySearchParameters
        );

        // then
        var alignedRefinements = alignedNav.getRefinements();
        assertThat(alignedRefinements).hasSize(1);

        var firstBucket = alignedRefinements.getFirst();
        assertThat(firstBucket.getCount()).isEqualTo(5); // Google count only, Mongo record not found due to the field mismatch
    }


    // Helper methods

    private Map<String, Object> createMongoMetadataWithPrice(Double price) {
        return Map.of(
            "indexables", Map.of(
                "priceInfo", Map.of(
                    "price", List.of(price)
                )
            )
        );
    }

    private Record createRecordWithPrice(Double price) {
        return Record.of(
            APPAREL_MERCHANDISER,
            COLLECTION,
            PRODUCT_PREFIX + price,
            PRODUCT_PREFIX + price,
            TEST_PRODUCT_TITLE,
            createMongoMetadataWithPrice(price)
        );
    }

    private Record createRecordWithStringPrice() {
        Map<String, Object> metadata = Map.of(
            "indexables", Map.of(
                "priceInfo", Map.of(
                    "price", List.of(INVALID_PRICE_VALUE)  // Invalid string value
                )
            )
        );

        return Record.of(
            APPAREL_MERCHANDISER,
            COLLECTION,
            STRING_PRODUCT_ID,
            STRING_PRODUCT_ID,
            TEST_PRODUCT_TITLE,
            metadata
        );
    }

    private Record createRecordWithNullPrice() {
        Map<String, Object> metadata = Map.of(
            "indexables", Map.of(
                "priceInfo", Map.of()  // Empty priceInfo - no price field
            )
        );

        return Record.of(
            APPAREL_MERCHANDISER,
            COLLECTION,
            NULL_PRODUCT_ID,
            NULL_PRODUCT_ID,
            TEST_PRODUCT_TITLE,
            metadata
        );
    }

    private NavigationRefinement findRefinementByRange(List<NavigationRefinement> refinements,
                                                       Double low, Double high) {
        return refinements.stream()
            .filter(r -> {
                var range = r.getRange();
                var rangeLow = range.low();
                var rangeHigh = range.high();
                return (Objects.equals(rangeLow, low)) &&
                       (Objects.equals(rangeHigh, high));
            })
            .findFirst()
            .orElseThrow(() -> new AssertionError("Expected Range [" + low + ", " + high + ") not found"));
    }
}
